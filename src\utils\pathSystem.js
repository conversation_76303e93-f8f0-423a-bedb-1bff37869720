/**
 * 路径系统 - 管理直线和贝塞尔曲线路径
 */
import * as THREE from 'three'

// 路径类型常量
export const PATH_TYPES = {
  STRAIGHT: 'straight',
  BEZIER: 'bezier'
}

// 路径方向常量
export const PATH_DIRECTIONS = {
  START_TO_END: 'start_to_end',
  END_TO_START: 'end_to_start',
  BIDIRECTIONAL: 'bidirectional'
}

/**
 * 基础路径类
 */
export class BasePath {
  constructor(type, name, mapId) {
    this.uuid = this.generateUUID()
    this.type = type
    this.name = name
    this.mapId = mapId
    this.direction = PATH_DIRECTIONS.START_TO_END
    this.linearVelocity = 1.0
    this.angularVelocity = 1.0
    this.description = ''
    this.isSaved = false

    // 性能相关属性
    this.mapComplexity = 0 // 地图复杂度，用于性能优化

    // 3D对象
    this.group = new THREE.Group()
    this.group.uuid = this.uuid // 确保group的uuid与路径对象的uuid一致
    this.line = null
    this.controlPoints = []
    this.isSelected = false
    this.isEditing = false

    // 设置用户数据
    this.group.userData = {
      draggable: false,
      objectType: 'path',
      objectUuid: this.uuid,
      pathType: this.type
    }
  }
  
  generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c === 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
  
  // 设置选中状态
  setSelected(selected) {
    this.isSelected = selected
    this.updateAppearance()
  }

  // 设置编辑状态
  setEditing(editing) {
    this.isEditing = editing
    this.updateAppearance()
  }

  // 设置地图复杂度（用于性能优化）
  setMapComplexity(complexity) {
    this.mapComplexity = complexity
  }
  
  // 更新外观（子类实现）
  updateAppearance() {
    // 子类重写
  }
  
  // 获取路径数据
  getPathData() {
    const data = {
      uuid: this.uuid,
      type: this.type,
      name: this.name,
      mapId: this.mapId,
      direction: this.direction,
      linearVelocity: this.linearVelocity,
      angularVelocity: this.angularVelocity,
      description: this.description,
      isSaved: this.isSaved
    }
    console.log(`获取路径数据: ${this.name}, 方向: ${this.direction}`, data)
    return data
  }
  
  // 更新属性
  updateProperty(property, value) {
    if (Object.prototype.hasOwnProperty.call(this, property)) {
      this[property] = value
    }
  }
  
  // 销毁对象
  dispose() {
    if (this.group) {
      this.group.traverse((child) => {
        if (child.geometry) child.geometry.dispose()
        if (child.material) {
          if (Array.isArray(child.material)) {
            child.material.forEach(mat => mat.dispose())
          } else {
            child.material.dispose()
          }
        }
      })
      
      if (this.group.parent) {
        this.group.parent.remove(this.group)
      }
    }
  }
}

/**
 * 路径管理器
 */
export class PathSystemManager {
  constructor() {
    this.paths = new Map() // uuid -> path对象
    this.pathCounters = new Map() // mapId -> {straight: counter, bezier: counter}
    this.selectedPath = null
    this.editingPath = null
  }
  
  // 添加路径
  addPath(path) {
    this.paths.set(path.uuid, path)
  }
  
  // 移除路径
  removePath(uuid) {
    const path = this.paths.get(uuid)
    if (path) {
      path.dispose()
      this.paths.delete(uuid)

      if (this.selectedPath === path) {
        this.selectedPath = null
      }
      if (this.editingPath === path) {
        this.editingPath = null
      }
    }
  }

  // 获取路径
  getPath(uuid) {
    return this.paths.get(uuid)
  }
  
  // 获取地图的所有路径
  getMapPaths(mapId) {
    return Array.from(this.paths.values()).filter(path =>
      path.mapId === mapId || path.mapId.toString() === mapId.toString()
    )
  }
  
  // 清除所有路径的选中状态和编辑状态
  clearAllSelections() {
    // 取消所有路径的选中状态和编辑状态
    this.paths.forEach((path) => {
      if (path.setSelected) {
        path.setSelected(false)
      }
      if (path.setEditing) {
        path.setEditing(false)
      }
    })
    this.selectedPath = null
    this.editingPath = null
  }

  // 选择路径
  selectPath(uuid) {
    const targetPath = this.paths.get(uuid)

    // 如果要选中的路径就是当前选中的路径，只需要确保它是选中状态，不要清除编辑状态
    if (this.selectedPath && this.selectedPath === targetPath) {
      this.selectedPath.setSelected(true)
      return this.selectedPath
    }

    // 取消之前的选择状态
    if (this.selectedPath) {
      this.selectedPath.setSelected(false)
    }

    // 取消之前的编辑状态（只有当编辑的不是要选中的路径时）
    if (this.editingPath && this.editingPath !== targetPath) {
      this.editingPath.setEditing(false)
      this.editingPath = null
    }

    // 选择新路径
    this.selectedPath = targetPath
    if (this.selectedPath) {
      this.selectedPath.setSelected(true)
    }

    return this.selectedPath
  }
  
  // 开始编辑路径
  startEditing(uuid) {
    const path = this.paths.get(uuid)
    if (path) {
      // 结束之前的编辑
      if (this.editingPath) {
        this.editingPath.setEditing(false)
      }
      
      this.editingPath = path
      path.setEditing(true)
    }
    return path
  }
  
  // 结束编辑
  stopEditing() {
    if (this.editingPath) {
      this.editingPath.setEditing(false)
      this.editingPath = null
    }
  }
  
  // 生成唯一路径名称
  generateUniquePathName(mapId, type) {
    const prefix = type === 'straight' ? '直线' : '曲线'

    // 获取或初始化该地图的计数器
    if (!this.pathCounters.has(mapId)) {
      this.pathCounters.set(mapId, { straight: 1, bezier: 1 })
    }

    const counters = this.pathCounters.get(mapId)
    let counter = counters[type]

    // 检查名称是否已存在
    const existingNames = this.getMapPaths(mapId).map(path => path.name)
    let name = `${prefix}${counter}`

    while (existingNames.includes(name)) {
      counter++
      name = `${prefix}${counter}`
    }

    // 更新计数器
    counters[type] = counter + 1
    return name
  }
  
  // 清理地图路径
  clearMapPaths(mapId) {
    const pathsToRemove = Array.from(this.paths.values())
      .filter(path => path.mapId === mapId)
    
    pathsToRemove.forEach(path => {
      this.removePath(path.uuid)
    })
  }
  
  // 获取所有路径数据
  getAllPathsData() {
    return Array.from(this.paths.values()).map(path => path.getPathData())
  }
}
